/* eslint-disable no-underscore-dangle */
/* eslint-disable camelcase */
const User = require('./userModel');

const providerCounter = require('./counter/providerCounter');

const logger = require('../../common/utils/logger');

const notificationService = require('../notification/email/notificationService');

const authService = require('../auth/authService');

const createUser = async (userData, groupRole, providerStatus) => {
    const key = await providerCounter.getNextSequence();
    const userId = `G_UID_${key}`;

    const { name, email, phone_number } = userData;
    const rawBirthdate = userData.birthdate || userData.raw?.birthdate;

    if (!rawBirthdate) {
        logger.error('Missing birthdate in user data', { email });
        throw new Error('Date of birth is required.');
    }

    const birthdate = new Date(rawBirthdate).toISOString().split('T')[0];

    const existingUser = await User.findOne({ email });

    if (existingUser) {
        logger.warn('User already exists with email', { email });
        throw new Error('User already exists with this email.');
    }

    let ISVerified = true;

    if (groupRole === 'provider') {
        try {
            await notificationService.sendKYCMail(email);
            ISVerified = false;
        } catch (error) {
            logger.error('Failed to send KYC email to provider', {
                email,
                error: error.message,
            });
            ISVerified = false;
        }
    }

    const user = new User({
        name,
        dateOfBirth: birthdate,
        mobile: phone_number,
        email,
        groupRole,
        ISVerified,
        userId,
        providerStatus,
    });

    try {
        logger.info(`Assigning user ${email} to group ${groupRole}`);
        await authService.assignUserToGroup(email, groupRole);
        logger.info(
            `User ${email} assigned to group ${groupRole} successfully.`
        );

        await authService.updateUserAttributes(
            email,
            userId,
            '',
            '',
            '',
            ''
        );
        logger.info(`User attributes updated for ${email}`);
    } catch (authError) {
        logger.error('Failed to assign user to group or update attributes', {
            email,
            error: authError.message,
        });
        throw new Error('Error in user authorization or group assignment.');
    }

    const result = await user.save();
    return result;
};

const getUsers = async (query, sortBy, sortDirection, pageNum, limitNum) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        const users = await User.find(query)
            .sort({ updatedAt: -1, [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await User.countDocuments(query);

        const formattedUsers = users.map(formatUpdatedUser);

        return { users: formattedUsers, total };
    } catch (error) {
        logger.error('Error fetching users:', error);
        throw new Error('Failed to retrieve users.');
    }
};
const getUserById = async (userId) => {
    const user = await User.findOne({ userId });
    return user ? formatUpdatedUser(user) : null;
};

const updateUser = async (userId, updateData) => {
    try {
        const user = await validateAndFetchUser(userId);
        const isVerified = await handleKYCVerification(user, updateData);

        const updatedFields = buildUpdatedFields(updateData, user);
        const updatedUser = await updateDatabaseUser(
            user.userId,
            updatedFields,
            isVerified
        );

        await syncAuthAttributes(user, updatedFields);

        return formatUpdatedUser(updatedUser);
    } catch (error) {
        logger.error('Error updating user:', error.message);
        throw error;
    }
};

const validateAndFetchUser = async (userId) => {
    if (!userId) {
        throw new Error('User ID is required');
    }

    const user = await getUserById(userId);
    if (!user) {
        throw new Error('User not found');
    }

    return user;
};

const handleKYCVerification = async (user, updateData) => {
    let isVerified = user.ISVerified;

    if (updateData.groupRole === 'provider') {
        isVerified = false;

        try {
            await notificationService.sendKYCMail(user.email);
        } catch (error) {
            logger.error('Error sending KYC email:', error);
        }
    }

    return isVerified;
};

const buildUpdatedFields = (updateData, user) => {
    const updatedFields = {};

    if (updateData.name) updatedFields.name = updateData.name;
    if (updateData.dateOfBirth)
        updatedFields.dateOfBirth = updateData.dateOfBirth;
    if (updateData.mobile) updatedFields.mobile = updateData.mobile;
    if (updateData.gender) updatedFields.gender = updateData.gender;

    if (updateData.groupRole) updatedFields.groupRole = updateData.groupRole;
    if (updateData.bio) updatedFields.bio = updateData.bio;
    if (updateData.profilePicture)
        updatedFields.profilePicture = updateData.profilePicture;
    if (updateData.currencyCode)
        updatedFields.currencyCode = updateData.currencyCode;
    if (updateData.language) updatedFields.language = updateData.language;
    if (updateData.providerStatus)
        updatedFields.providerStatus = updateData.providerStatus;

    if (typeof updateData.IsActive === 'boolean')
        updatedFields.IsActive = updateData.IsActive;

    if (
        updateData.address &&
        typeof updateData.address === 'object' &&
        !Array.isArray(updateData.address)
    ) {
        updatedFields.address = {
            ...user.address,
            ...updateData.address,
        };
    }

    if (updateData.email && updateData.email !== user.email) {
        logger.warn('Attempted to update email. Email update is not allowed.');
    }

    return updatedFields;
};

const updateDatabaseUser = async (userId, updatedFields, isVerified) => {
    return await User.findOneAndUpdate(
        { userId },
        { $set: { ...updatedFields, ISVerified: isVerified } },
        { new: true, runValidators: true }
    );
};

const syncAuthAttributes = async (user, updatedFields) => {
    await authService.updateUserAttributes(
        user.email,
        '',
        updatedFields.name || user.name,
        updatedFields.mobile || user.mobile,
        updatedFields.dateOfBirth || user.dateOfBirth,
        updatedFields.groupRole || user.groupRole
    );
};

const formatUpdatedUser = (updatedUser) => ({
    _id: updatedUser._id,
    userId: updatedUser.userId,
    name: updatedUser.name,
    email: updatedUser.email,
    mobile: updatedUser.mobile,
    gender: updatedUser.gender,
    dateOfBirth: updatedUser.dateOfBirth,
    groupRole: updatedUser.groupRole,
    bio: updatedUser.bio,
    address: updatedUser.address,
    currencyCode: updatedUser.currencyCode,
    language: updatedUser.language,
    profilePicture: updatedUser.profilePicture,
    ISVerified: updatedUser.ISVerified,
    providerStatus: updatedUser.providerStatus,
    IsActive: updatedUser.IsActive,
    createdAt: updatedUser.createdAt,
    updatedAt: updatedUser.updatedAt,
    __v: updatedUser.__v,
});

const deleteUser = async (userId) => {
    try {
        if (!userId) throw new Error('User ID is required');

        const user = await getUserById(userId);
        if (!user) throw new Error('User not found');

        const deletedUser = await User.findOneAndDelete({ userId });
        if (!deletedUser)
            throw new Error('Failed to delete user from the database');
        await authService.deleteUser(user.email);

        return deletedUser;
    } catch (error) {
        throw new Error(error.message || 'An unknown error occurred');
    }
};

const becomeAProvider = async (userId) => {
    try {
        const user = await getUserById(userId);

        if (!user) throw new Error('User not found');

        logger.info(`Assigning user ${user.email} to group 'provider'`);
        await authService.assignUserToGroup(user.email, 'provider');

        logger.info(`Updating user ${userId} to provider`);
        const updatedUser = await updateUser(userId, {
            groupRole: 'provider',
        });

        triggerKYCEmail(user.email);

        return updatedUser;
    } catch (error) {
        logger.error('Error in becomeAProvider:', error);
        throw error;
    }
};

const triggerKYCEmail = async (email) => {
    try {
        logger.info(`Sending KYC email to ${email}`);
        await notificationService.sendKYCMail(email);
    } catch (error) {
        logger.warn(`Failed to send KYC email to ${email}:`, error.message);
    }
};

const getUnverifiedUserEmails = async () => {
    try {
        const users = await User.find({ ISVerified: false }).select('email');

        const emails = users.map((user) => user.email);

        return emails;
    } catch (error) {
        logger.error('Error fetching unverified users:', error);
        throw new Error('Failed to retrieve unverified users.');
    }
};

const updateISVerifiedStatus = async (email, isVerified) => {
    try {
        const updatedUser = await User.findOneAndUpdate(
            { email },
            { $set: { ISVerified: isVerified } },
            { new: true, runValidators: true }
        );

        if (!updatedUser) {
            return { message: 'User not found with the provided email.' };
        }

        return updatedUser;
    } catch (error) {
        logger.error(`Error updating ISVerified status:${error.message}`);
        throw new Error('Failed to update ISVerified status.');
    }
};
module.exports = {
    createUser,
    getUsers,
    getUserById,
    updateUser,
    deleteUser,
    becomeAProvider,
    getUnverifiedUserEmails,
    updateISVerifiedStatus,
};
